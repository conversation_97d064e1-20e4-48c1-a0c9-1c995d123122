'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Target, TrendingUp, BookOpen, Info } from 'lucide-react';
import { toast } from 'sonner';
import SkillAssessmentForm from '@/components/skills/SkillAssessmentForm';
import SkillGapAnalysis from '@/components/skills/SkillGapAnalysis';

interface Skill {
  id: string;
  name: string;
  category: string;
  description?: string;
}

interface SkillAssessment {
  skillId: string;
  skillName: string;
  selfRating: number;
  confidenceLevel: number;
  assessmentType: 'SELF_ASSESSMENT' | 'PEER_VALIDATION' | 'CERTIFICATION' | 'PERFORMANCE_BASED' | 'AI_EVALUATED';
  notes?: string;
  yearsOfExperience?: number;
  lastUsed?: string;
}

interface ComprehensiveAnalysisRequest {
  currentSkills: Array<{
    skillName: string;
    selfRating: number;
    confidenceLevel: number;
    yearsOfExperience?: number;
  }>;
  targetCareerPath: {
    careerPathName: string;
    targetLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  };
  preferences: {
    timeframe: 'THREE_MONTHS' | 'SIX_MONTHS' | 'ONE_YEAR' | 'TWO_YEARS';
    hoursPerWeek: number;
    learningStyle: string[];
    budget: 'FREE' | 'FREEMIUM' | 'PAID';
    focusAreas: string[];
  };
  includeMarketData: boolean;
  includePersonalizedPaths: boolean;
}

interface AnalysisResult {
  analysisId: string;
  skillGaps: any[];
  learningPlan: any;
  careerReadiness: any;
  marketInsights?: any;
  generatedAt: string;
  cached?: boolean;
}

export default function SkillGapAnalyzerPage() {
  const [activeTab, setActiveTab] = useState('assess');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [userAssessments, setUserAssessments] = useState<SkillAssessment[]>([]);
  const [targetCareerPath, setTargetCareerPath] = useState('');
  const [targetLevel, setTargetLevel] = useState<'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'>('INTERMEDIATE');
  const [preferences, setPreferences] = useState({
    timeframe: 'ONE_YEAR' as const,
    hoursPerWeek: 10,
    learningStyle: ['VISUAL'],
    budget: 'FREEMIUM' as const,
    focusAreas: ['Technical Skills'],
  });

  // Load existing assessments on component mount
  useEffect(() => {
    loadUserAssessments();
  }, []);

  const loadUserAssessments = async () => {
    try {
      const response = await fetch('/api/skills/assessment');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.assessments) {
          // Convert API response to SkillAssessment format
          const assessments: SkillAssessment[] = data.data.assessments.map((assessment: any) => ({
            skillId: assessment.skillId,
            skillName: assessment.skillName,
            selfRating: assessment.currentRating,
            confidenceLevel: assessment.confidenceLevel,
            assessmentType: 'SELF_ASSESSMENT' as const,
            lastUsed: assessment.lastAssessed,
          }));
          setUserAssessments(assessments);
        }
      }
    } catch (error) {
      console.error('Error loading user assessments:', error);
    }
  };

  const searchSkills = async (query: string): Promise<Skill[]> => {
    try {
      const response = await fetch(`/api/skills/search?q=${encodeURIComponent(query)}`);
      if (response.ok) {
        const data = await response.json();
        return data.skills || [];
      }
    } catch (error) {
      console.error('Error searching skills:', error);
    }
    return [];
  };

  const handleSkillAssessment = async (assessments: SkillAssessment[]) => {
    try {
      const response = await fetch('/api/skills/assessment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(
          assessments.length === 1
            ? assessments[0]
            : { assessments }
        ),
      });

      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Please log in to save your skill assessments', {
            action: {
              label: 'Log In',
              onClick: () => window.location.href = '/login?callbackUrl=' + encodeURIComponent(window.location.pathname),
            },
          });
          return;
        }
        throw new Error(`HTTP ${response.status}: Failed to submit assessments`);
      }

      const data = await response.json();
      if (data.success) {
        toast.success('Skill assessments submitted successfully!');
        await loadUserAssessments(); // Reload assessments

        // If user has assessments, suggest running analysis
        if (assessments.length > 0) {
          toast.info('Ready to analyze your skill gaps!', {
            action: {
              label: 'Analyze Now',
              onClick: () => setActiveTab('analyze'),
            },
          });
        }
      } else {
        throw new Error(data.error || 'Failed to submit assessments');
      }
    } catch (error) {
      console.error('Error submitting assessments:', error);
      if (error instanceof Error && error.message.includes('401')) {
        toast.error('Authentication required to save assessments');
      } else {
        toast.error('Failed to submit skill assessments. Please try again.');
      }
      throw error;
    }
  };

  const handleComprehensiveAnalysis = async () => {
    if (userAssessments.length === 0) {
      toast.error('Please complete skill assessments first');
      setActiveTab('assess');
      return;
    }

    if (!targetCareerPath.trim()) {
      toast.error('Please specify your target career path');
      return;
    }

    setIsAnalyzing(true);
    try {
      const analysisRequest: ComprehensiveAnalysisRequest = {
        currentSkills: userAssessments.map(assessment => ({
          skillName: assessment.skillName,
          selfRating: assessment.selfRating,
          confidenceLevel: assessment.confidenceLevel,
          yearsOfExperience: assessment.yearsOfExperience,
        })),
        targetCareerPath: {
          careerPathName: targetCareerPath,
          targetLevel,
        },
        preferences,
        includeMarketData: true,
        includePersonalizedPaths: true,
      };

      const response = await fetch('/api/ai/skills-analysis/comprehensive', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(analysisRequest),
      });

      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Please log in to run skill gap analysis', {
            action: {
              label: 'Log In',
              onClick: () => window.location.href = '/login?callbackUrl=' + encodeURIComponent(window.location.pathname),
            },
          });
          return;
        }
        throw new Error(`HTTP ${response.status}: Failed to analyze skill gaps`);
      }

      const data = await response.json();
      if (data.success) {
        setAnalysisResult(data.data);
        setActiveTab('results');
        toast.success('Skill gap analysis completed!');
      } else {
        throw new Error(data.error || 'Failed to analyze skill gaps');
      }
    } catch (error) {
      console.error('Error analyzing skill gaps:', error);
      if (error instanceof Error && error.message.includes('401')) {
        toast.error('Authentication required for skill gap analysis');
      } else {
        toast.error('Failed to analyze skill gaps. Please try again.');
      }
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Skill Gap Analyzer</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Assess your current skills, identify gaps, and get personalized learning recommendations 
          to advance your career goals.
        </p>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="assess" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Assess Skills
          </TabsTrigger>
          <TabsTrigger value="analyze" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Analyze Gaps
          </TabsTrigger>
          <TabsTrigger value="results" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            View Results
          </TabsTrigger>
        </TabsList>

        {/* Skill Assessment Tab */}
        <TabsContent value="assess" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Step 1: Assess Your Current Skills</CardTitle>
              <CardDescription>
                Rate your proficiency and confidence in various skills to establish your baseline.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {userAssessments.length > 0 && (
                <Alert className="mb-6">
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    You have {userAssessments.length} existing skill assessments. 
                    You can add more or proceed to gap analysis.
                  </AlertDescription>
                </Alert>
              )}
              
              <SkillAssessmentForm
                onSubmit={handleSkillAssessment}
                onSkillSearch={searchSkills}
                mode="bulk"
                maxAssessments={20}
                preserveStateOnSubmit={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Gap Analysis Tab */}
        <TabsContent value="analyze" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Step 2: Analyze Your Skill Gaps</CardTitle>
              <CardDescription>
                Define your career goals and preferences to get personalized gap analysis.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {userAssessments.length === 0 ? (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Please complete skill assessments first before running gap analysis.
                    <Button 
                      variant="link" 
                      className="p-0 ml-2 h-auto"
                      onClick={() => setActiveTab('assess')}
                    >
                      Go to assessments
                    </Button>
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Career Path Input */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Target Career Path</label>
                      <input
                        type="text"
                        placeholder="e.g., Full Stack Developer, Data Scientist"
                        value={targetCareerPath}
                        onChange={(e) => setTargetCareerPath(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    {/* Target Level */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Target Level</label>
                      <select
                        value={targetLevel}
                        onChange={(e) => setTargetLevel(e.target.value as any)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="BEGINNER">Beginner</option>
                        <option value="INTERMEDIATE">Intermediate</option>
                        <option value="ADVANCED">Advanced</option>
                        <option value="EXPERT">Expert</option>
                      </select>
                    </div>

                    {/* Timeframe */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Learning Timeframe</label>
                      <select
                        value={preferences.timeframe}
                        onChange={(e) => setPreferences(prev => ({ ...prev, timeframe: e.target.value as any }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="THREE_MONTHS">3 Months</option>
                        <option value="SIX_MONTHS">6 Months</option>
                        <option value="ONE_YEAR">1 Year</option>
                        <option value="TWO_YEARS">2 Years</option>
                      </select>
                    </div>

                    {/* Hours per Week */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Hours per Week</label>
                      <input
                        type="number"
                        min="1"
                        max="40"
                        value={preferences.hoursPerWeek}
                        onChange={(e) => setPreferences(prev => ({ ...prev, hoursPerWeek: parseInt(e.target.value) || 10 }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <Button
                      onClick={handleComprehensiveAnalysis}
                      disabled={isAnalyzing || !targetCareerPath.trim()}
                      size="lg"
                      className="min-w-48"
                    >
                      {isAnalyzing ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Analyzing...
                        </>
                      ) : (
                        'Analyze Skill Gaps'
                      )}
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-6">
          {analysisResult ? (
            <SkillGapAnalysis
              analysisId={analysisResult.analysisId}
              skillGaps={analysisResult.skillGaps}
              learningPlan={analysisResult.learningPlan}
              careerReadiness={analysisResult.careerReadiness}
              marketInsights={analysisResult.marketInsights}
              generatedAt={analysisResult.generatedAt}
              cached={analysisResult.cached}
            />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto" />
                  <div>
                    <h3 className="text-lg font-medium">No Analysis Results</h3>
                    <p className="text-gray-500">
                      Complete your skill assessment and run gap analysis to see results here.
                    </p>
                  </div>
                  <Button 
                    onClick={() => setActiveTab('assess')}
                    variant="outline"
                  >
                    Start Assessment
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
